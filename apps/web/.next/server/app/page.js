/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPSUyRlVzZXJzJTJGeCUyRkRvd25sb2FkcyUyRnZpYmUlMkYlRTYlOTclQTUlRTYlOEElQTUlMjBhdWclMkZhcHBzJTJGd2ViJTJGc3JjJTJGYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj0lMkZVc2VycyUyRnglMkZEb3dubG9hZHMlMkZ2aWJlJTJGJUU2JTk3JUE1JUU2JThBJUE1JTIwYXVnJTJGYXBwcyUyRndlYiZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGFBQWEsc0JBQXNCO0FBQ2lFO0FBQ3JDO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQyx1QkFBdUIsZ0pBQTZGO0FBQ3BIO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBLHlCQUF5QixvSkFBK0Y7QUFDeEgsb0JBQW9CLDBOQUFnRjtBQUNwRztBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFDNkQ7QUFDcEYsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDdUQ7QUFDdkQ7QUFDTyx3QkFBd0IsOEdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL0B0cmVuZHB1bHNlL3dlYi8/Y2RhMSJdLCJzb3VyY2VzQ29udGVudCI6WyJcIlRVUkJPUEFDSyB7IHRyYW5zaXRpb246IG5leHQtc3NyIH1cIjtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMveC9Eb3dubG9hZHMvdmliZS/ml6XmiqUgYXVnL2FwcHMvd2ViL3NyYy9hcHAvcGFnZS50c3hcIiksIFwiL1VzZXJzL3gvRG93bmxvYWRzL3ZpYmUv5pel5oqlIGF1Zy9hcHBzL3dlYi9zcmMvYXBwL3BhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3gvRG93bmxvYWRzL3ZpYmUv5pel5oqlIGF1Zy9hcHBzL3dlYi9zcmMvYXBwL2xheW91dC50c3hcIiksIFwiL1VzZXJzL3gvRG93bmxvYWRzL3ZpYmUv5pel5oqlIGF1Zy9hcHBzL3dlYi9zcmMvYXBwL2xheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIi9Vc2Vycy94L0Rvd25sb2Fkcy92aWJlL+aXpeaKpSBhdWcvYXBwcy93ZWIvc3JjL2FwcC9wYWdlLnRzeFwiXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiO1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL3BhZ2VcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCIsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fsrc%2Fcomponents%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fsrc%2Fcomponents%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(ssr)/./src/components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGeCUyRkRvd25sb2FkcyUyRnZpYmUlMkYlRTYlOTclQTUlRTYlOEElQTUlMjBhdWclMkZhcHBzJTJGd2ViJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGeCUyRkRvd25sb2FkcyUyRnZpYmUlMkYlRTYlOTclQTUlRTYlOEElQTUlMjBhdWclMkZhcHBzJTJGd2ViJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ4JTJGRG93bmxvYWRzJTJGdmliZSUyRiVFNiU5NyVBNSVFNiU4QSVBNSUyMGF1ZyUyRmFwcHMlMkZ3ZWIlMkZzcmMlMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlByb3ZpZGVycyUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQXdJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHRyZW5kcHVsc2Uvd2ViLz8yMWQwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUHJvdmlkZXJzXCJdICovIFwiL1VzZXJzL3gvRG93bmxvYWRzL3ZpYmUv5pel5oqlIGF1Zy9hcHBzL3dlYi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fsrc%2Fcomponents%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    retry: (failureCount, error)=>{\n                        if (error?.status === 404) return false;\n                        return failureCount < 3;\n                    }\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.QueryClientProvider, {\n            client: queryClient,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"system\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"hsl(var(--card))\",\n                                color: \"hsl(var(--card-foreground))\",\n                                border: \"1px solid hsl(var(--border))\"\n                            },\n                            success: {\n                                iconTheme: {\n                                    primary: \"hsl(var(--primary))\",\n                                    secondary: \"hsl(var(--primary-foreground))\"\n                                }\n                            },\n                            error: {\n                                iconTheme: {\n                                    primary: \"hsl(var(--destructive))\",\n                                    secondary: \"hsl(var(--destructive-foreground))\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/components/providers.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/components/providers.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/components/providers.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/components/providers.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7e0d5bdbb0a9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHRyZW5kcHVsc2Uvd2ViLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz84YTg3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiN2UwZDViZGJiMGE5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"TrendPulse - AI-Powered Trend Analysis\",\n    description: \"Get daily trend analysis reports powered by AI. Track keywords, analyze sentiment, and stay ahead of the curve.\",\n    keywords: [\n        \"trend analysis\",\n        \"AI\",\n        \"reports\",\n        \"keywords\",\n        \"sentiment analysis\"\n    ],\n    authors: [\n        {\n            name: \"TrendPulse Team\"\n        }\n    ],\n    creator: \"TrendPulse\",\n    publisher: \"TrendPulse\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(process.env.APP_URL || \"http://localhost:3000\"),\n    openGraph: {\n        title: \"TrendPulse - AI-Powered Trend Analysis\",\n        description: \"Get daily trend analysis reports powered by AI. Track keywords, analyze sentiment, and stay ahead of the curve.\",\n        url: \"/\",\n        siteName: \"TrendPulse\",\n        images: [\n            {\n                url: \"/og-image.png\",\n                width: 1200,\n                height: 630,\n                alt: \"TrendPulse - AI-Powered Trend Analysis\"\n            }\n        ],\n        locale: \"en_US\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"TrendPulse - AI-Powered Trend Analysis\",\n        description: \"Get daily trend analysis reports powered by AI. Track keywords, analyze sentiment, and stay ahead of the curve.\",\n        images: [\n            \"/og-image.png\"\n        ],\n        creator: \"@trendpulse\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_1__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/layout.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/layout.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/layout.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSDZDO0FBQzVCO0FBSWhCLE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7SUFDYkMsVUFBVTtRQUFDO1FBQWtCO1FBQU07UUFBVztRQUFZO0tBQXFCO0lBQy9FQyxTQUFTO1FBQUM7WUFBRUMsTUFBTTtRQUFrQjtLQUFFO0lBQ3RDQyxTQUFTO0lBQ1RDLFdBQVc7SUFDWEMsaUJBQWlCO1FBQ2ZDLE9BQU87UUFDUEMsU0FBUztRQUNUQyxXQUFXO0lBQ2I7SUFDQUMsY0FBYyxJQUFJQyxJQUFJQyxRQUFRQyxHQUFHLENBQUNDLE9BQU8sSUFBSTtJQUM3Q0MsV0FBVztRQUNUaEIsT0FBTztRQUNQQyxhQUFhO1FBQ2JnQixLQUFLO1FBQ0xDLFVBQVU7UUFDVkMsUUFBUTtZQUNOO2dCQUNFRixLQUFLO2dCQUNMRyxPQUFPO2dCQUNQQyxRQUFRO2dCQUNSQyxLQUFLO1lBQ1A7U0FDRDtRQUNEQyxRQUFRO1FBQ1JDLE1BQU07SUFDUjtJQUNBQyxTQUFTO1FBQ1BDLE1BQU07UUFDTjFCLE9BQU87UUFDUEMsYUFBYTtRQUNia0IsUUFBUTtZQUFDO1NBQWdCO1FBQ3pCZCxTQUFTO0lBQ1g7SUFDQXNCLFFBQVE7UUFDTkMsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLFdBQVc7WUFDVEYsT0FBTztZQUNQQyxRQUFRO1lBQ1IscUJBQXFCLENBQUM7WUFDdEIscUJBQXFCO1lBQ3JCLGVBQWUsQ0FBQztRQUNsQjtJQUNGO0lBQ0FFLGNBQWM7UUFDWkMsUUFBUTtJQUNWO0FBQ0YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO1FBQUtDLHdCQUF3QjtrQkFDdEMsNEVBQUNDO1lBQUtDLFdBQVcxQywrSkFBZTtzQkFDOUIsNEVBQUNDLDREQUFTQTswQkFDUG9DOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AdHJlbmRwdWxzZS93ZWIvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnO1xuaW1wb3J0IHsgUHJvdmlkZXJzIH0gZnJvbSAnQC9jb21wb25lbnRzL3Byb3ZpZGVycyc7XG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnO1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1RyZW5kUHVsc2UgLSBBSS1Qb3dlcmVkIFRyZW5kIEFuYWx5c2lzJyxcbiAgZGVzY3JpcHRpb246ICdHZXQgZGFpbHkgdHJlbmQgYW5hbHlzaXMgcmVwb3J0cyBwb3dlcmVkIGJ5IEFJLiBUcmFjayBrZXl3b3JkcywgYW5hbHl6ZSBzZW50aW1lbnQsIGFuZCBzdGF5IGFoZWFkIG9mIHRoZSBjdXJ2ZS4nLFxuICBrZXl3b3JkczogWyd0cmVuZCBhbmFseXNpcycsICdBSScsICdyZXBvcnRzJywgJ2tleXdvcmRzJywgJ3NlbnRpbWVudCBhbmFseXNpcyddLFxuICBhdXRob3JzOiBbeyBuYW1lOiAnVHJlbmRQdWxzZSBUZWFtJyB9XSxcbiAgY3JlYXRvcjogJ1RyZW5kUHVsc2UnLFxuICBwdWJsaXNoZXI6ICdUcmVuZFB1bHNlJyxcbiAgZm9ybWF0RGV0ZWN0aW9uOiB7XG4gICAgZW1haWw6IGZhbHNlLFxuICAgIGFkZHJlc3M6IGZhbHNlLFxuICAgIHRlbGVwaG9uZTogZmFsc2UsXG4gIH0sXG4gIG1ldGFkYXRhQmFzZTogbmV3IFVSTChwcm9jZXNzLmVudi5BUFBfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjMwMDAnKSxcbiAgb3BlbkdyYXBoOiB7XG4gICAgdGl0bGU6ICdUcmVuZFB1bHNlIC0gQUktUG93ZXJlZCBUcmVuZCBBbmFseXNpcycsXG4gICAgZGVzY3JpcHRpb246ICdHZXQgZGFpbHkgdHJlbmQgYW5hbHlzaXMgcmVwb3J0cyBwb3dlcmVkIGJ5IEFJLiBUcmFjayBrZXl3b3JkcywgYW5hbHl6ZSBzZW50aW1lbnQsIGFuZCBzdGF5IGFoZWFkIG9mIHRoZSBjdXJ2ZS4nLFxuICAgIHVybDogJy8nLFxuICAgIHNpdGVOYW1lOiAnVHJlbmRQdWxzZScsXG4gICAgaW1hZ2VzOiBbXG4gICAgICB7XG4gICAgICAgIHVybDogJy9vZy1pbWFnZS5wbmcnLFxuICAgICAgICB3aWR0aDogMTIwMCxcbiAgICAgICAgaGVpZ2h0OiA2MzAsXG4gICAgICAgIGFsdDogJ1RyZW5kUHVsc2UgLSBBSS1Qb3dlcmVkIFRyZW5kIEFuYWx5c2lzJyxcbiAgICAgIH0sXG4gICAgXSxcbiAgICBsb2NhbGU6ICdlbl9VUycsXG4gICAgdHlwZTogJ3dlYnNpdGUnLFxuICB9LFxuICB0d2l0dGVyOiB7XG4gICAgY2FyZDogJ3N1bW1hcnlfbGFyZ2VfaW1hZ2UnLFxuICAgIHRpdGxlOiAnVHJlbmRQdWxzZSAtIEFJLVBvd2VyZWQgVHJlbmQgQW5hbHlzaXMnLFxuICAgIGRlc2NyaXB0aW9uOiAnR2V0IGRhaWx5IHRyZW5kIGFuYWx5c2lzIHJlcG9ydHMgcG93ZXJlZCBieSBBSS4gVHJhY2sga2V5d29yZHMsIGFuYWx5emUgc2VudGltZW50LCBhbmQgc3RheSBhaGVhZCBvZiB0aGUgY3VydmUuJyxcbiAgICBpbWFnZXM6IFsnL29nLWltYWdlLnBuZyddLFxuICAgIGNyZWF0b3I6ICdAdHJlbmRwdWxzZScsXG4gIH0sXG4gIHJvYm90czoge1xuICAgIGluZGV4OiB0cnVlLFxuICAgIGZvbGxvdzogdHJ1ZSxcbiAgICBnb29nbGVCb3Q6IHtcbiAgICAgIGluZGV4OiB0cnVlLFxuICAgICAgZm9sbG93OiB0cnVlLFxuICAgICAgJ21heC12aWRlby1wcmV2aWV3JzogLTEsXG4gICAgICAnbWF4LWltYWdlLXByZXZpZXcnOiAnbGFyZ2UnLFxuICAgICAgJ21heC1zbmlwcGV0JzogLTEsXG4gICAgfSxcbiAgfSxcbiAgdmVyaWZpY2F0aW9uOiB7XG4gICAgZ29vZ2xlOiAneW91ci1nb29nbGUtdmVyaWZpY2F0aW9uLWNvZGUnLFxuICB9LFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAgPFByb3ZpZGVycz5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvUHJvdmlkZXJzPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIlByb3ZpZGVycyIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiYXV0aG9ycyIsIm5hbWUiLCJjcmVhdG9yIiwicHVibGlzaGVyIiwiZm9ybWF0RGV0ZWN0aW9uIiwiZW1haWwiLCJhZGRyZXNzIiwidGVsZXBob25lIiwibWV0YWRhdGFCYXNlIiwiVVJMIiwicHJvY2VzcyIsImVudiIsIkFQUF9VUkwiLCJvcGVuR3JhcGgiLCJ1cmwiLCJzaXRlTmFtZSIsImltYWdlcyIsIndpZHRoIiwiaGVpZ2h0IiwiYWx0IiwibG9jYWxlIiwidHlwZSIsInR3aXR0ZXIiLCJjYXJkIiwicm9ib3RzIiwiaW5kZXgiLCJmb2xsb3ciLCJnb29nbGVCb3QiLCJ2ZXJpZmljYXRpb24iLCJnb29nbGUiLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsInN1cHByZXNzSHlkcmF0aW9uV2FybmluZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BellIcon_ChartBarIcon_ClockIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BellIcon,ChartBarIcon,ClockIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BellIcon_ChartBarIcon_ClockIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BellIcon,ChartBarIcon,ClockIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BellIcon_ChartBarIcon_ClockIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BellIcon,ChartBarIcon,ClockIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BellIcon_ChartBarIcon_ClockIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BellIcon,ChartBarIcon,ClockIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BellIcon_ChartBarIcon_ClockIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BellIcon,ChartBarIcon,ClockIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BellIcon_ChartBarIcon_ClockIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BellIcon,ChartBarIcon,ClockIcon,DocumentTextIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n\n\n// 模拟数据\nconst mockStats = [\n    {\n        name: \"Keywords Tracked\",\n        value: \"12,847\",\n        change: \"+12%\"\n    },\n    {\n        name: \"Reports Generated\",\n        value: \"3,421\",\n        change: \"+8%\"\n    },\n    {\n        name: \"Active Users\",\n        value: \"1,234\",\n        change: \"+15%\"\n    },\n    {\n        name: \"Accuracy Rate\",\n        value: \"94.7%\",\n        change: \"+2%\"\n    }\n];\nconst mockTrendingTopics = [\n    {\n        topic: \"AI Technology\",\n        mentions: 15420,\n        sentiment: \"positive\",\n        change: \"+23%\"\n    },\n    {\n        topic: \"Climate Change\",\n        mentions: 12890,\n        sentiment: \"neutral\",\n        change: \"+8%\"\n    },\n    {\n        topic: \"Cryptocurrency\",\n        mentions: 9876,\n        sentiment: \"mixed\",\n        change: \"-5%\"\n    },\n    {\n        topic: \"Remote Work\",\n        mentions: 8765,\n        sentiment: \"positive\",\n        change: \"+12%\"\n    },\n    {\n        topic: \"Electric Vehicles\",\n        mentions: 7654,\n        sentiment: \"positive\",\n        change: \"+18%\"\n    }\n];\nconst mockRecentReports = [\n    {\n        title: \"Weekly AI Trends Analysis\",\n        date: \"2024-07-08\",\n        summary: \"AI adoption continues to surge across industries with 23% increase in mentions.\",\n        keywords: [\n            \"AI\",\n            \"Machine Learning\",\n            \"Automation\"\n        ]\n    },\n    {\n        title: \"Climate Tech Investment Report\",\n        date: \"2024-07-07\",\n        summary: \"Green technology investments show strong growth with focus on renewable energy.\",\n        keywords: [\n            \"Climate Tech\",\n            \"Investment\",\n            \"Renewable Energy\"\n        ]\n    },\n    {\n        title: \"Social Media Sentiment Analysis\",\n        date: \"2024-07-06\",\n        summary: \"Consumer sentiment remains positive with increased engagement in tech discussions.\",\n        keywords: [\n            \"Social Media\",\n            \"Sentiment\",\n            \"Consumer Behavior\"\n        ]\n    }\n];\nconst features = [\n    {\n        name: \"AI-Powered Analysis\",\n        description: \"Advanced AI algorithms analyze trends, sentiment, and provide actionable insights.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BellIcon_ChartBarIcon_ClockIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n    },\n    {\n        name: \"Daily Reports\",\n        description: \"Automated daily reports delivered to your inbox at your preferred time.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BellIcon_ChartBarIcon_ClockIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    },\n    {\n        name: \"Real-time Monitoring\",\n        description: \"Track keywords across news, social media, and search trends in real-time.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BellIcon_ChartBarIcon_ClockIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    {\n        name: \"Smart Notifications\",\n        description: \"Get notified when significant changes or trending topics emerge.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BellIcon_ChartBarIcon_ClockIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    }\n];\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-16 items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-8 rounded-lg bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BellIcon_ChartBarIcon_ClockIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-5 w-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: \"TrendPulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/auth/signin\",\n                                        className: \"px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/auth/signup\",\n                                        className: \"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-white py-16 sm:py-24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl\",\n                                children: [\n                                    \"AI-Powered\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: \"Trend Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-6 text-lg leading-8 text-gray-600 max-w-2xl mx-auto\",\n                                children: \"Stay ahead of the curve with daily AI-generated trend reports. Track keywords, analyze sentiment, and get actionable insights delivered to your inbox.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-10 flex items-center justify-center gap-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/auth/signup\",\n                                        className: \"flex items-center px-6 py-3 text-base font-semibold text-white bg-blue-600 rounded-md hover:bg-blue-700\",\n                                        children: [\n                                            \"Start Free Trial\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BellIcon_ChartBarIcon_ClockIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"ml-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#demo\",\n                                        className: \"px-6 py-3 text-base font-semibold text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                        children: \"View Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                    children: \"Live Platform Statistics\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-lg text-gray-600\",\n                                    children: \"Real-time data from our trend analysis platform\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\",\n                            children: mockStats.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: stat.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-gray-900\",\n                                                        children: stat.value\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-sm text-green-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-1\",\n                                                        children: \"↗\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    stat.change\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this)\n                                }, stat.name, false, {\n                                    fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"demo\",\n                className: \"py-16 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl text-blue-600 mr-2\",\n                                                children: \"\\uD83D\\uDCC8\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Trending Topics\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: mockTrendingTopics.map((topic, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-gray-900\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    index + 1,\n                                                                    \" \",\n                                                                    topic.topic\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `px-2 py-1 text-xs rounded-full ${topic.sentiment === \"positive\" ? \"bg-green-100 text-green-800\" : topic.sentiment === \"negative\" ? \"bg-red-100 text-red-800\" : topic.sentiment === \"mixed\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-gray-100 text-gray-800\"}`,\n                                                                children: topic.sentiment\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    topic.mentions.toLocaleString(),\n                                                                    \" mentions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `font-medium ${topic.change.startsWith(\"+\") ? \"text-green-600\" : \"text-red-600\"}`,\n                                                                children: topic.change\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, topic.topic, true, {\n                                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BellIcon_ChartBarIcon_ClockIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-600 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Recent Reports\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: mockRecentReports.map((report)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 rounded-lg p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-gray-900\",\n                                                                children: report.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: report.date\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-4\",\n                                                        children: report.summary\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2\",\n                                                        children: report.keywords.map((keyword)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\",\n                                                                children: keyword\n                                                            }, keyword, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, report.title, true, {\n                                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                    children: \"Powerful Features\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-lg text-gray-600\",\n                                    children: \"Everything you need to stay ahead of trends\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4\",\n                            children: features.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center w-12 h-12 bg-blue-600 rounded-lg mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: feature.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: feature.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, feature.name, true, {\n                                    fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                lineNumber: 221,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-blue-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold tracking-tight text-white sm:text-4xl\",\n                                children: \"Ready to get started?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-6 text-lg leading-8 text-blue-100 max-w-2xl mx-auto\",\n                                children: \"Join thousands of professionals who rely on TrendPulse for their daily trend analysis. Start your free trial today and see the difference AI-powered insights can make.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-10 flex items-center justify-center gap-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/auth/signup\",\n                                        className: \"px-8 py-3 text-base font-semibold text-blue-600 bg-white rounded-md hover:bg-gray-50 transition-colors\",\n                                        children: \"Start Your Free Trial\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/auth/signin\",\n                                        className: \"px-8 py-3 text-base font-semibold text-white border border-white rounded-md hover:bg-blue-700 transition-colors\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-6 rounded bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BellIcon_ChartBarIcon_ClockIcon_DocumentTextIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: \"TrendPulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"\\xa9 2024 TrendPulse. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/app/page.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFPcUM7QUFFckMsT0FBTztBQUNQLE1BQU1NLFlBQVk7SUFDaEI7UUFBRUMsTUFBTTtRQUFvQkMsT0FBTztRQUFVQyxRQUFRO0lBQU87SUFDNUQ7UUFBRUYsTUFBTTtRQUFxQkMsT0FBTztRQUFTQyxRQUFRO0lBQU07SUFDM0Q7UUFBRUYsTUFBTTtRQUFnQkMsT0FBTztRQUFTQyxRQUFRO0lBQU87SUFDdkQ7UUFBRUYsTUFBTTtRQUFpQkMsT0FBTztRQUFTQyxRQUFRO0lBQU07Q0FDeEQ7QUFFRCxNQUFNQyxxQkFBcUI7SUFDekI7UUFBRUMsT0FBTztRQUFpQkMsVUFBVTtRQUFPQyxXQUFXO1FBQVlKLFFBQVE7SUFBTztJQUNqRjtRQUFFRSxPQUFPO1FBQWtCQyxVQUFVO1FBQU9DLFdBQVc7UUFBV0osUUFBUTtJQUFNO0lBQ2hGO1FBQUVFLE9BQU87UUFBa0JDLFVBQVU7UUFBTUMsV0FBVztRQUFTSixRQUFRO0lBQU07SUFDN0U7UUFBRUUsT0FBTztRQUFlQyxVQUFVO1FBQU1DLFdBQVc7UUFBWUosUUFBUTtJQUFPO0lBQzlFO1FBQUVFLE9BQU87UUFBcUJDLFVBQVU7UUFBTUMsV0FBVztRQUFZSixRQUFRO0lBQU87Q0FDckY7QUFFRCxNQUFNSyxvQkFBb0I7SUFDeEI7UUFDRUMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsVUFBVTtZQUFDO1lBQU07WUFBb0I7U0FBYTtJQUNwRDtJQUNBO1FBQ0VILE9BQU87UUFDUEMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFVBQVU7WUFBQztZQUFnQjtZQUFjO1NBQW1CO0lBQzlEO0lBQ0E7UUFDRUgsT0FBTztRQUNQQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsVUFBVTtZQUFDO1lBQWdCO1lBQWE7U0FBb0I7SUFDOUQ7Q0FDRDtBQUVELE1BQU1DLFdBQVc7SUFDZjtRQUNFWixNQUFNO1FBQ05hLGFBQWE7UUFDYkMsTUFBTWxCLHNLQUFZQTtJQUNwQjtJQUNBO1FBQ0VJLE1BQU07UUFDTmEsYUFBYTtRQUNiQyxNQUFNcEIsc0tBQVNBO0lBQ2pCO0lBQ0E7UUFDRU0sTUFBTTtRQUNOYSxhQUFhO1FBQ2JDLE1BQU1yQixzS0FBWUE7SUFDcEI7SUFDQTtRQUNFTyxNQUFNO1FBQ05hLGFBQWE7UUFDYkMsTUFBTW5CLHNLQUFRQTtJQUNoQjtDQUNEO0FBRWMsU0FBU29CO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0M7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUN4QixzS0FBWUE7NENBQUN3QixXQUFVOzs7Ozs7Ozs7OztrREFFMUIsOERBQUNFO3dDQUFLRixXQUFVO2tEQUErRjs7Ozs7Ozs7Ozs7OzBDQUlqSCw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRzt3Q0FBRUMsTUFBSzt3Q0FBZUosV0FBVTtrREFBa0U7Ozs7OztrREFHbkcsOERBQUNHO3dDQUFFQyxNQUFLO3dDQUFlSixXQUFVO2tEQUFvRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTN0gsOERBQUNLO2dCQUFRTCxXQUFVOzBCQUNqQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ007Z0NBQUdOLFdBQVU7O29DQUE4RDtvQ0FDL0Q7a0RBQ1gsOERBQUNFO3dDQUFLRixXQUFVO2tEQUE2RTs7Ozs7Ozs7Ozs7OzBDQUkvRiw4REFBQ087Z0NBQUVQLFdBQVU7MENBQXlEOzs7Ozs7MENBSXRFLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNHO3dDQUFFQyxNQUFLO3dDQUFlSixXQUFVOzs0Q0FBMEc7MERBRXpJLDhEQUFDcEIsc0tBQWNBO2dEQUFDb0IsV0FBVTs7Ozs7Ozs7Ozs7O2tEQUU1Qiw4REFBQ0c7d0NBQUVDLE1BQUs7d0NBQVFKLFdBQVU7a0RBQXFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVN2SSw4REFBQ0s7Z0JBQVFMLFdBQVU7MEJBQ2pCLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ1E7b0NBQUdSLFdBQVU7OENBQThEOzs7Ozs7OENBRzVFLDhEQUFDTztvQ0FBRVAsV0FBVTs4Q0FBNkI7Ozs7Ozs7Ozs7OztzQ0FJNUMsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNabEIsVUFBVTJCLEdBQUcsQ0FBQyxDQUFDQyxxQkFDZCw4REFBQ1g7b0NBQW9CQyxXQUFVOzhDQUM3Qiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDs7a0VBQ0MsOERBQUNRO3dEQUFFUCxXQUFVO2tFQUFxQ1UsS0FBSzNCLElBQUk7Ozs7OztrRUFDM0QsOERBQUN3Qjt3REFBRVAsV0FBVTtrRUFBb0NVLEtBQUsxQixLQUFLOzs7Ozs7Ozs7Ozs7MERBRTdELDhEQUFDZTtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNFO3dEQUFLRixXQUFVO2tFQUFPOzs7Ozs7b0RBQ3RCVSxLQUFLekIsTUFBTTs7Ozs7Ozs7Ozs7OzttQ0FSUnlCLEtBQUszQixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBa0IzQiw4REFBQ3NCO2dCQUFRTSxJQUFHO2dCQUFPWCxXQUFVOzBCQUMzQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ0Q7O2tEQUNDLDhEQUFDQTt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNFO2dEQUFLRixXQUFVOzBEQUE4Qjs7Ozs7OzBEQUM5Qyw4REFBQ1k7Z0RBQUdaLFdBQVU7MERBQW1DOzs7Ozs7Ozs7Ozs7a0RBRW5ELDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDWmQsbUJBQW1CdUIsR0FBRyxDQUFDLENBQUN0QixPQUFPMEIsc0JBQzlCLDhEQUFDZDtnREFBc0JDLFdBQVU7O2tFQUMvQiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDYztnRUFBR2QsV0FBVTs7b0VBQThCO29FQUFFYSxRQUFRO29FQUFFO29FQUFFMUIsTUFBTUEsS0FBSzs7Ozs7OzswRUFDckUsOERBQUNlO2dFQUFLRixXQUFXLENBQUMsK0JBQStCLEVBQy9DYixNQUFNRSxTQUFTLEtBQUssYUFBYSxnQ0FDakNGLE1BQU1FLFNBQVMsS0FBSyxhQUFhLDRCQUNqQ0YsTUFBTUUsU0FBUyxLQUFLLFVBQVUsa0NBQzlCLDRCQUNELENBQUM7MEVBQ0NGLE1BQU1FLFNBQVM7Ozs7Ozs7Ozs7OztrRUFHcEIsOERBQUNVO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0U7O29FQUFNZixNQUFNQyxRQUFRLENBQUMyQixjQUFjO29FQUFHOzs7Ozs7OzBFQUN2Qyw4REFBQ2I7Z0VBQUtGLFdBQVcsQ0FBQyxZQUFZLEVBQzVCYixNQUFNRixNQUFNLENBQUMrQixVQUFVLENBQUMsT0FBTyxtQkFBbUIsZUFDbkQsQ0FBQzswRUFDQzdCLE1BQU1GLE1BQU07Ozs7Ozs7Ozs7Ozs7K0NBakJURSxNQUFNQSxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7OzBDQTBCM0IsOERBQUNZOztrREFDQyw4REFBQ0E7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDbkIsc0tBQWdCQTtnREFBQ21CLFdBQVU7Ozs7OzswREFDNUIsOERBQUNZO2dEQUFHWixXQUFVOzBEQUFtQzs7Ozs7Ozs7Ozs7O2tEQUVuRCw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1pWLGtCQUFrQm1CLEdBQUcsQ0FBQyxDQUFDUSx1QkFDdEIsOERBQUNsQjtnREFBdUJDLFdBQVU7O2tFQUNoQyw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDYztnRUFBR2QsV0FBVTswRUFBK0JpQixPQUFPMUIsS0FBSzs7Ozs7OzBFQUN6RCw4REFBQ1c7Z0VBQUtGLFdBQVU7MEVBQXlCaUIsT0FBT3pCLElBQUk7Ozs7Ozs7Ozs7OztrRUFFdEQsOERBQUNlO3dEQUFFUCxXQUFVO2tFQUFzQmlCLE9BQU94QixPQUFPOzs7Ozs7a0VBQ2pELDhEQUFDTTt3REFBSUMsV0FBVTtrRUFDWmlCLE9BQU92QixRQUFRLENBQUNlLEdBQUcsQ0FBQyxDQUFDUyx3QkFDcEIsOERBQUNoQjtnRUFBbUJGLFdBQVU7MEVBQzNCa0I7K0RBRFFBOzs7Ozs7Ozs7OzsrQ0FSUEQsT0FBTzFCLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQXNCbEMsOERBQUNjO2dCQUFRTCxXQUFVOzBCQUNqQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNRO29DQUFHUixXQUFVOzhDQUE4RDs7Ozs7OzhDQUc1RSw4REFBQ087b0NBQUVQLFdBQVU7OENBQTZCOzs7Ozs7Ozs7Ozs7c0NBSTVDLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDWkwsU0FBU2MsR0FBRyxDQUFDLENBQUNVLHdCQUNiLDhEQUFDcEI7b0NBQXVCQyxXQUFVOztzREFDaEMsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDbUIsUUFBUXRCLElBQUk7Z0RBQUNHLFdBQVU7Ozs7Ozs7Ozs7O3NEQUUxQiw4REFBQ1k7NENBQUdaLFdBQVU7c0RBQTRDbUIsUUFBUXBDLElBQUk7Ozs7OztzREFDdEUsOERBQUN3Qjs0Q0FBRVAsV0FBVTtzREFBaUJtQixRQUFRdkIsV0FBVzs7Ozs7OzttQ0FMekN1QixRQUFRcEMsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQWE5Qiw4REFBQ3NCO2dCQUFRTCxXQUFVOzBCQUNqQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ1E7Z0NBQUdSLFdBQVU7MENBQTJEOzs7Ozs7MENBR3pFLDhEQUFDTztnQ0FBRVAsV0FBVTswQ0FBeUQ7Ozs7OzswQ0FJdEUsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0c7d0NBQUVDLE1BQUs7d0NBQWVKLFdBQVU7a0RBQXlHOzs7Ozs7a0RBRzFJLDhEQUFDRzt3Q0FBRUMsTUFBSzt3Q0FBZUosV0FBVTtrREFBa0g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUzNKLDhEQUFDb0I7Z0JBQU9wQixXQUFVOzBCQUNoQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ3hCLHNLQUFZQTs0Q0FBQ3dCLFdBQVU7Ozs7Ozs7Ozs7O2tEQUUxQiw4REFBQ0U7d0NBQUtGLFdBQVU7a0RBQThCOzs7Ozs7Ozs7Ozs7MENBRWhELDhEQUFDTztnQ0FBRVAsV0FBVTswQ0FBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRakQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AdHJlbmRwdWxzZS93ZWIvLi9zcmMvYXBwL3BhZ2UudHN4P2Y2OGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgQ2hhcnRCYXJJY29uLFxuICBDbG9ja0ljb24sXG4gIEJlbGxJY29uLFxuICBTcGFya2xlc0ljb24sXG4gIEFycm93UmlnaHRJY29uLFxuICBEb2N1bWVudFRleHRJY29uLFxufSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuXG4vLyDmqKHmi5/mlbDmja5cbmNvbnN0IG1vY2tTdGF0cyA9IFtcbiAgeyBuYW1lOiAnS2V5d29yZHMgVHJhY2tlZCcsIHZhbHVlOiAnMTIsODQ3JywgY2hhbmdlOiAnKzEyJScgfSxcbiAgeyBuYW1lOiAnUmVwb3J0cyBHZW5lcmF0ZWQnLCB2YWx1ZTogJzMsNDIxJywgY2hhbmdlOiAnKzglJyB9LFxuICB7IG5hbWU6ICdBY3RpdmUgVXNlcnMnLCB2YWx1ZTogJzEsMjM0JywgY2hhbmdlOiAnKzE1JScgfSxcbiAgeyBuYW1lOiAnQWNjdXJhY3kgUmF0ZScsIHZhbHVlOiAnOTQuNyUnLCBjaGFuZ2U6ICcrMiUnIH0sXG5dO1xuXG5jb25zdCBtb2NrVHJlbmRpbmdUb3BpY3MgPSBbXG4gIHsgdG9waWM6ICdBSSBUZWNobm9sb2d5JywgbWVudGlvbnM6IDE1NDIwLCBzZW50aW1lbnQ6ICdwb3NpdGl2ZScsIGNoYW5nZTogJysyMyUnIH0sXG4gIHsgdG9waWM6ICdDbGltYXRlIENoYW5nZScsIG1lbnRpb25zOiAxMjg5MCwgc2VudGltZW50OiAnbmV1dHJhbCcsIGNoYW5nZTogJys4JScgfSxcbiAgeyB0b3BpYzogJ0NyeXB0b2N1cnJlbmN5JywgbWVudGlvbnM6IDk4NzYsIHNlbnRpbWVudDogJ21peGVkJywgY2hhbmdlOiAnLTUlJyB9LFxuICB7IHRvcGljOiAnUmVtb3RlIFdvcmsnLCBtZW50aW9uczogODc2NSwgc2VudGltZW50OiAncG9zaXRpdmUnLCBjaGFuZ2U6ICcrMTIlJyB9LFxuICB7IHRvcGljOiAnRWxlY3RyaWMgVmVoaWNsZXMnLCBtZW50aW9uczogNzY1NCwgc2VudGltZW50OiAncG9zaXRpdmUnLCBjaGFuZ2U6ICcrMTglJyB9LFxuXTtcblxuY29uc3QgbW9ja1JlY2VudFJlcG9ydHMgPSBbXG4gIHtcbiAgICB0aXRsZTogJ1dlZWtseSBBSSBUcmVuZHMgQW5hbHlzaXMnLFxuICAgIGRhdGU6ICcyMDI0LTA3LTA4JyxcbiAgICBzdW1tYXJ5OiAnQUkgYWRvcHRpb24gY29udGludWVzIHRvIHN1cmdlIGFjcm9zcyBpbmR1c3RyaWVzIHdpdGggMjMlIGluY3JlYXNlIGluIG1lbnRpb25zLicsXG4gICAga2V5d29yZHM6IFsnQUknLCAnTWFjaGluZSBMZWFybmluZycsICdBdXRvbWF0aW9uJ10sXG4gIH0sXG4gIHtcbiAgICB0aXRsZTogJ0NsaW1hdGUgVGVjaCBJbnZlc3RtZW50IFJlcG9ydCcsXG4gICAgZGF0ZTogJzIwMjQtMDctMDcnLFxuICAgIHN1bW1hcnk6ICdHcmVlbiB0ZWNobm9sb2d5IGludmVzdG1lbnRzIHNob3cgc3Ryb25nIGdyb3d0aCB3aXRoIGZvY3VzIG9uIHJlbmV3YWJsZSBlbmVyZ3kuJyxcbiAgICBrZXl3b3JkczogWydDbGltYXRlIFRlY2gnLCAnSW52ZXN0bWVudCcsICdSZW5ld2FibGUgRW5lcmd5J10sXG4gIH0sXG4gIHtcbiAgICB0aXRsZTogJ1NvY2lhbCBNZWRpYSBTZW50aW1lbnQgQW5hbHlzaXMnLFxuICAgIGRhdGU6ICcyMDI0LTA3LTA2JyxcbiAgICBzdW1tYXJ5OiAnQ29uc3VtZXIgc2VudGltZW50IHJlbWFpbnMgcG9zaXRpdmUgd2l0aCBpbmNyZWFzZWQgZW5nYWdlbWVudCBpbiB0ZWNoIGRpc2N1c3Npb25zLicsXG4gICAga2V5d29yZHM6IFsnU29jaWFsIE1lZGlhJywgJ1NlbnRpbWVudCcsICdDb25zdW1lciBCZWhhdmlvciddLFxuICB9LFxuXTtcblxuY29uc3QgZmVhdHVyZXMgPSBbXG4gIHtcbiAgICBuYW1lOiAnQUktUG93ZXJlZCBBbmFseXNpcycsXG4gICAgZGVzY3JpcHRpb246ICdBZHZhbmNlZCBBSSBhbGdvcml0aG1zIGFuYWx5emUgdHJlbmRzLCBzZW50aW1lbnQsIGFuZCBwcm92aWRlIGFjdGlvbmFibGUgaW5zaWdodHMuJyxcbiAgICBpY29uOiBTcGFya2xlc0ljb24sXG4gIH0sXG4gIHtcbiAgICBuYW1lOiAnRGFpbHkgUmVwb3J0cycsXG4gICAgZGVzY3JpcHRpb246ICdBdXRvbWF0ZWQgZGFpbHkgcmVwb3J0cyBkZWxpdmVyZWQgdG8geW91ciBpbmJveCBhdCB5b3VyIHByZWZlcnJlZCB0aW1lLicsXG4gICAgaWNvbjogQ2xvY2tJY29uLFxuICB9LFxuICB7XG4gICAgbmFtZTogJ1JlYWwtdGltZSBNb25pdG9yaW5nJyxcbiAgICBkZXNjcmlwdGlvbjogJ1RyYWNrIGtleXdvcmRzIGFjcm9zcyBuZXdzLCBzb2NpYWwgbWVkaWEsIGFuZCBzZWFyY2ggdHJlbmRzIGluIHJlYWwtdGltZS4nLFxuICAgIGljb246IENoYXJ0QmFySWNvbixcbiAgfSxcbiAge1xuICAgIG5hbWU6ICdTbWFydCBOb3RpZmljYXRpb25zJyxcbiAgICBkZXNjcmlwdGlvbjogJ0dldCBub3RpZmllZCB3aGVuIHNpZ25pZmljYW50IGNoYW5nZXMgb3IgdHJlbmRpbmcgdG9waWNzIGVtZXJnZS4nLFxuICAgIGljb246IEJlbGxJY29uLFxuICB9LFxuXTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFnZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwXCI+XG4gICAgICB7LyogTmF2aWdhdGlvbiAqL31cbiAgICAgIDxuYXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBtYXgtdy03eGwgcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaC0xNiBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCB3LTggcm91bmRlZC1sZyBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNjAwIHRvLXB1cnBsZS02MDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8Q2hhcnRCYXJJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8tcHVycGxlLTYwMCBiZy1jbGlwLXRleHQgdGV4dC10cmFuc3BhcmVudFwiPlxuICAgICAgICAgICAgICAgIFRyZW5kUHVsc2VcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICA8YSBocmVmPVwiL2F1dGgvc2lnbmluXCIgY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBob3Zlcjp0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgU2lnbiBJblxuICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgIDxhIGhyZWY9XCIvYXV0aC9zaWdudXBcIiBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlIGJnLWJsdWUtNjAwIHJvdW5kZWQtbWQgaG92ZXI6YmctYmx1ZS03MDBcIj5cbiAgICAgICAgICAgICAgICBHZXQgU3RhcnRlZFxuICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L25hdj5cblxuICAgICAgey8qIEhlcm8gU2VjdGlvbiAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cImJnLXdoaXRlIHB5LTE2IHNtOnB5LTI0XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBtYXgtdy03eGwgcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIHRyYWNraW5nLXRpZ2h0IHRleHQtZ3JheS05MDAgc206dGV4dC02eGxcIj5cbiAgICAgICAgICAgICAgQUktUG93ZXJlZHsnICd9XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1wdXJwbGUtNjAwIGJnLWNsaXAtdGV4dCB0ZXh0LXRyYW5zcGFyZW50XCI+XG4gICAgICAgICAgICAgICAgVHJlbmQgQW5hbHlzaXNcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTYgdGV4dC1sZyBsZWFkaW5nLTggdGV4dC1ncmF5LTYwMCBtYXgtdy0yeGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICBTdGF5IGFoZWFkIG9mIHRoZSBjdXJ2ZSB3aXRoIGRhaWx5IEFJLWdlbmVyYXRlZCB0cmVuZCByZXBvcnRzLiBUcmFjayBrZXl3b3JkcyxcbiAgICAgICAgICAgICAgYW5hbHl6ZSBzZW50aW1lbnQsIGFuZCBnZXQgYWN0aW9uYWJsZSBpbnNpZ2h0cyBkZWxpdmVyZWQgdG8geW91ciBpbmJveC5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ2FwLXgtNlwiPlxuICAgICAgICAgICAgICA8YSBocmVmPVwiL2F1dGgvc2lnbnVwXCIgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcHgtNiBweS0zIHRleHQtYmFzZSBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgYmctYmx1ZS02MDAgcm91bmRlZC1tZCBob3ZlcjpiZy1ibHVlLTcwMFwiPlxuICAgICAgICAgICAgICAgIFN0YXJ0IEZyZWUgVHJpYWxcbiAgICAgICAgICAgICAgICA8QXJyb3dSaWdodEljb24gY2xhc3NOYW1lPVwibWwtMiBoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICA8YSBocmVmPVwiI2RlbW9cIiBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1iYXNlIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgaG92ZXI6YmctZ3JheS01MFwiPlxuICAgICAgICAgICAgICAgIFZpZXcgRGVtb1xuICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG5cbiAgICAgIHsvKiBMaXZlIFN0YXRzIFNlY3Rpb24gKi99XG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0xNiBiZy1ncmF5LTUwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBtYXgtdy03eGwgcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTEyXCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRyYWNraW5nLXRpZ2h0IHRleHQtZ3JheS05MDAgc206dGV4dC00eGxcIj5cbiAgICAgICAgICAgICAgTGl2ZSBQbGF0Zm9ybSBTdGF0aXN0aWNzXG4gICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LWxnIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgUmVhbC10aW1lIGRhdGEgZnJvbSBvdXIgdHJlbmQgYW5hbHlzaXMgcGxhdGZvcm1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgZ2FwLTYgc206Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTRcIj5cbiAgICAgICAgICAgIHttb2NrU3RhdHMubWFwKChzdGF0KSA9PiAoXG4gICAgICAgICAgICAgIDxkaXYga2V5PXtzdGF0Lm5hbWV9IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LXNtIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcC02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMFwiPntzdGF0Lm5hbWV9PC9wPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPntzdGF0LnZhbHVlfTwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LXNtIHRleHQtZ3JlZW4tNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTFcIj7ihpc8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIHtzdGF0LmNoYW5nZX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIERlbW8gU2VjdGlvbiAtIFRyZW5kaW5nIFRvcGljcyAqL31cbiAgICAgIDxzZWN0aW9uIGlkPVwiZGVtb1wiIGNsYXNzTmFtZT1cInB5LTE2IGJnLXdoaXRlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBtYXgtdy03eGwgcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTIgZ2FwLTEyXCI+XG4gICAgICAgICAgICB7LyogVHJlbmRpbmcgVG9waWNzICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBtYi02XCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0yeGwgdGV4dC1ibHVlLTYwMCBtci0yXCI+8J+TiDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5UcmVuZGluZyBUb3BpY3M8L2gzPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICB7bW9ja1RyZW5kaW5nVG9waWNzLm1hcCgodG9waWMsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGtleT17dG9waWMudG9waWN9IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj4je2luZGV4ICsgMX0ge3RvcGljLnRvcGljfTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgcHgtMiBweS0xIHRleHQteHMgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICAgICAgICB0b3BpYy5zZW50aW1lbnQgPT09ICdwb3NpdGl2ZScgPyAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICB0b3BpYy5zZW50aW1lbnQgPT09ICduZWdhdGl2ZScgPyAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgIHRvcGljLnNlbnRpbWVudCA9PT0gJ21peGVkJyA/ICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAge3RvcGljLnNlbnRpbWVudH1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57dG9waWMubWVudGlvbnMudG9Mb2NhbGVTdHJpbmcoKX0gbWVudGlvbnM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRvcGljLmNoYW5nZS5zdGFydHNXaXRoKCcrJykgPyAndGV4dC1ncmVlbi02MDAnIDogJ3RleHQtcmVkLTYwMCdcbiAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7dG9waWMuY2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFJlY2VudCBSZXBvcnRzICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBtYi02XCI+XG4gICAgICAgICAgICAgICAgPERvY3VtZW50VGV4dEljb24gY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LWJsdWUtNjAwIG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPlJlY2VudCBSZXBvcnRzPC9oMz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAge21vY2tSZWNlbnRSZXBvcnRzLm1hcCgocmVwb3J0KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGtleT17cmVwb3J0LnRpdGxlfSBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHJvdW5kZWQtbGcgcC02XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBqdXN0aWZ5LWJldHdlZW4gbWItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj57cmVwb3J0LnRpdGxlfTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+e3JlcG9ydC5kYXRlfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPntyZXBvcnQuc3VtbWFyeX08L3A+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7cmVwb3J0LmtleXdvcmRzLm1hcCgoa2V5d29yZCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4ga2V5PXtrZXl3b3JkfSBjbGFzc05hbWU9XCJweC0yIHB5LTEgYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCB0ZXh0LXhzIHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7a2V5d29yZH1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG5cbiAgICAgIHsvKiBGZWF0dXJlcyBTZWN0aW9uICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicHktMTYgYmctZ3JheS01MFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm14LWF1dG8gbWF4LXctN3hsIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xMlwiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0cmFja2luZy10aWdodCB0ZXh0LWdyYXktOTAwIHNtOnRleHQtNHhsXCI+XG4gICAgICAgICAgICAgIFBvd2VyZnVsIEZlYXR1cmVzXG4gICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LWxnIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgRXZlcnl0aGluZyB5b3UgbmVlZCB0byBzdGF5IGFoZWFkIG9mIHRyZW5kc1xuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBnYXAtOCBzbTpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNFwiPlxuICAgICAgICAgICAge2ZlYXR1cmVzLm1hcCgoZmVhdHVyZSkgPT4gKFxuICAgICAgICAgICAgICA8ZGl2IGtleT17ZmVhdHVyZS5uYW1lfSBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdy0xMiBoLTEyIGJnLWJsdWUtNjAwIHJvdW5kZWQtbGcgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgPGZlYXR1cmUuaWNvbiBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+e2ZlYXR1cmUubmFtZX08L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj57ZmVhdHVyZS5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICB7LyogQ1RBIFNlY3Rpb24gKi99XG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0xNiBiZy1ibHVlLTYwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm14LWF1dG8gbWF4LXctN3hsIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0cmFja2luZy10aWdodCB0ZXh0LXdoaXRlIHNtOnRleHQtNHhsXCI+XG4gICAgICAgICAgICAgIFJlYWR5IHRvIGdldCBzdGFydGVkP1xuICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTYgdGV4dC1sZyBsZWFkaW5nLTggdGV4dC1ibHVlLTEwMCBtYXgtdy0yeGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICBKb2luIHRob3VzYW5kcyBvZiBwcm9mZXNzaW9uYWxzIHdobyByZWx5IG9uIFRyZW5kUHVsc2UgZm9yIHRoZWlyIGRhaWx5IHRyZW5kIGFuYWx5c2lzLlxuICAgICAgICAgICAgICBTdGFydCB5b3VyIGZyZWUgdHJpYWwgdG9kYXkgYW5kIHNlZSB0aGUgZGlmZmVyZW5jZSBBSS1wb3dlcmVkIGluc2lnaHRzIGNhbiBtYWtlLlxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBnYXAteC02XCI+XG4gICAgICAgICAgICAgIDxhIGhyZWY9XCIvYXV0aC9zaWdudXBcIiBjbGFzc05hbWU9XCJweC04IHB5LTMgdGV4dC1iYXNlIGZvbnQtc2VtaWJvbGQgdGV4dC1ibHVlLTYwMCBiZy13aGl0ZSByb3VuZGVkLW1kIGhvdmVyOmJnLWdyYXktNTAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICBTdGFydCBZb3VyIEZyZWUgVHJpYWxcbiAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICA8YSBocmVmPVwiL2F1dGgvc2lnbmluXCIgY2xhc3NOYW1lPVwicHgtOCBweS0zIHRleHQtYmFzZSBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgYm9yZGVyIGJvcmRlci13aGl0ZSByb3VuZGVkLW1kIGhvdmVyOmJnLWJsdWUtNzAwIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgU2lnbiBJblxuICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG5cbiAgICAgIHsvKiBGb290ZXIgKi99XG4gICAgICA8Zm9vdGVyIGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlci10IGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm14LWF1dG8gbWF4LXctN3hsIHB4LTQgcHktMTIgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC02IHctNiByb3VuZGVkIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS02MDAgdG8tcHVycGxlLTYwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxDaGFydEJhckljb24gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlRyZW5kUHVsc2U8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICDCqSAyMDI0IFRyZW5kUHVsc2UuIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9mb290ZXI+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiQ2hhcnRCYXJJY29uIiwiQ2xvY2tJY29uIiwiQmVsbEljb24iLCJTcGFya2xlc0ljb24iLCJBcnJvd1JpZ2h0SWNvbiIsIkRvY3VtZW50VGV4dEljb24iLCJtb2NrU3RhdHMiLCJuYW1lIiwidmFsdWUiLCJjaGFuZ2UiLCJtb2NrVHJlbmRpbmdUb3BpY3MiLCJ0b3BpYyIsIm1lbnRpb25zIiwic2VudGltZW50IiwibW9ja1JlY2VudFJlcG9ydHMiLCJ0aXRsZSIsImRhdGUiLCJzdW1tYXJ5Iiwia2V5d29yZHMiLCJmZWF0dXJlcyIsImRlc2NyaXB0aW9uIiwiaWNvbiIsIlBhZ2UiLCJkaXYiLCJjbGFzc05hbWUiLCJuYXYiLCJzcGFuIiwiYSIsImhyZWYiLCJzZWN0aW9uIiwiaDEiLCJwIiwiaDIiLCJtYXAiLCJzdGF0IiwiaWQiLCJoMyIsImluZGV4IiwiaDQiLCJ0b0xvY2FsZVN0cmluZyIsInN0YXJ0c1dpdGgiLCJyZXBvcnQiLCJrZXl3b3JkIiwiZmVhdHVyZSIsImZvb3RlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/vibe/日报 aug/apps/web/src/components/providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@tanstack","vendor-chunks/react-hot-toast","vendor-chunks/next-themes","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fx%2FDownloads%2Fvibe%2F%E6%97%A5%E6%8A%A5%20aug%2Fapps%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();