import {
  ChartBarIcon,
  ClockIcon,
  BellIcon,
  SparklesIcon,
  ArrowRightIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';

// 模拟数据
const mockStats = [
  { name: 'Keywords Tracked', value: '12,847', change: '+12%' },
  { name: 'Reports Generated', value: '3,421', change: '+8%' },
  { name: 'Active Users', value: '1,234', change: '+15%' },
  { name: 'Accuracy Rate', value: '94.7%', change: '+2%' },
];

const mockTrendingTopics = [
  { topic: 'AI Technology', mentions: 15420, sentiment: 'positive', change: '+23%' },
  { topic: 'Climate Change', mentions: 12890, sentiment: 'neutral', change: '+8%' },
  { topic: 'Cryptocurrency', mentions: 9876, sentiment: 'mixed', change: '-5%' },
  { topic: 'Remote Work', mentions: 8765, sentiment: 'positive', change: '+12%' },
  { topic: 'Electric Vehicles', mentions: 7654, sentiment: 'positive', change: '+18%' },
];

const mockRecentReports = [
  {
    title: 'Weekly AI Trends Analysis',
    date: '2024-07-08',
    summary: 'AI adoption continues to surge across industries with 23% increase in mentions.',
    keywords: ['AI', 'Machine Learning', 'Automation'],
  },
  {
    title: 'Climate Tech Investment Report',
    date: '2024-07-07',
    summary: 'Green technology investments show strong growth with focus on renewable energy.',
    keywords: ['Climate Tech', 'Investment', 'Renewable Energy'],
  },
  {
    title: 'Social Media Sentiment Analysis',
    date: '2024-07-06',
    summary: 'Consumer sentiment remains positive with increased engagement in tech discussions.',
    keywords: ['Social Media', 'Sentiment', 'Consumer Behavior'],
  },
];

const features = [
  {
    name: 'AI-Powered Analysis',
    description: 'Advanced AI algorithms analyze trends, sentiment, and provide actionable insights.',
    icon: SparklesIcon,
  },
  {
    name: 'Daily Reports',
    description: 'Automated daily reports delivered to your inbox at your preferred time.',
    icon: ClockIcon,
  },
  {
    name: 'Real-time Monitoring',
    description: 'Track keywords across news, social media, and search trends in real-time.',
    icon: ChartBarIcon,
  },
  {
    name: 'Smart Notifications',
    description: 'Get notified when significant changes or trending topics emerge.',
    icon: BellIcon,
  },
];

export default function Page() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center">
                <ChartBarIcon className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                TrendPulse
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <a href="/auth/signin" className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900">
                Sign In
              </a>
              <a href="/auth/signup" className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                Get Started
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-white py-16 sm:py-24">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              AI-Powered{' '}
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Trend Analysis
              </span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600 max-w-2xl mx-auto">
              Stay ahead of the curve with daily AI-generated trend reports. Track keywords,
              analyze sentiment, and get actionable insights delivered to your inbox.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <a href="/auth/signup" className="flex items-center px-6 py-3 text-base font-semibold text-white bg-blue-600 rounded-md hover:bg-blue-700">
                Start Free Trial
                <ArrowRightIcon className="ml-2 h-4 w-4" />
              </a>
              <a href="#demo" className="px-6 py-3 text-base font-semibold text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50">
                View Demo
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Live Stats Section */}
      <section className="py-16 bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Live Platform Statistics
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Real-time data from our trend analysis platform
            </p>
          </div>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {mockStats.map((stat) => (
              <div key={stat.name} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                    <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <div className="flex items-center text-sm text-green-600">
                    <span className="mr-1">↗</span>
                    {stat.change}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Demo Section - Trending Topics */}
      <section id="demo" className="py-16 bg-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Trending Topics */}
            <div>
              <div className="flex items-center mb-6">
                <span className="text-2xl text-blue-600 mr-2">📈</span>
                <h3 className="text-2xl font-bold text-gray-900">Trending Topics</h3>
              </div>
              <div className="space-y-4">
                {mockTrendingTopics.map((topic, index) => (
                  <div key={topic.topic} className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold text-gray-900">#{index + 1} {topic.topic}</h4>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        topic.sentiment === 'positive' ? 'bg-green-100 text-green-800' :
                        topic.sentiment === 'negative' ? 'bg-red-100 text-red-800' :
                        topic.sentiment === 'mixed' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {topic.sentiment}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>{topic.mentions.toLocaleString()} mentions</span>
                      <span className={`font-medium ${
                        topic.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {topic.change}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Recent Reports */}
            <div>
              <div className="flex items-center mb-6">
                <DocumentTextIcon className="h-6 w-6 text-blue-600 mr-2" />
                <h3 className="text-2xl font-bold text-gray-900">Recent Reports</h3>
              </div>
              <div className="space-y-6">
                {mockRecentReports.map((report) => (
                  <div key={report.title} className="bg-gray-50 rounded-lg p-6">
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="font-semibold text-gray-900">{report.title}</h4>
                      <span className="text-sm text-gray-500">{report.date}</span>
                    </div>
                    <p className="text-gray-600 mb-4">{report.summary}</p>
                    <div className="flex flex-wrap gap-2">
                      {report.keywords.map((keyword) => (
                        <span key={keyword} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                          {keyword}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Powerful Features
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Everything you need to stay ahead of trends
            </p>
          </div>
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
            {features.map((feature) => (
              <div key={feature.name} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-center w-12 h-12 bg-blue-600 rounded-lg mb-4">
                  <feature.icon className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.name}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Ready to get started?
            </h2>
            <p className="mt-6 text-lg leading-8 text-blue-100 max-w-2xl mx-auto">
              Join thousands of professionals who rely on TrendPulse for their daily trend analysis.
              Start your free trial today and see the difference AI-powered insights can make.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <a href="/auth/signup" className="px-8 py-3 text-base font-semibold text-blue-600 bg-white rounded-md hover:bg-gray-50 transition-colors">
                Start Your Free Trial
              </a>
              <a href="/auth/signin" className="px-8 py-3 text-base font-semibold text-white border border-white rounded-md hover:bg-blue-700 transition-colors">
                Sign In
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200">
        <div className="mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-6 w-6 rounded bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center">
                <ChartBarIcon className="h-4 w-4 text-white" />
              </div>
              <span className="font-semibold text-gray-900">TrendPulse</span>
            </div>
            <p className="text-sm text-gray-500">
              © 2024 TrendPulse. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
